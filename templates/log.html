INFO: Received M2VM request.
DEBUG: Output directory set to: /Users/<USER>/Desktop/ce-ps3-product-repo/migration/m2vm
DEBUG: Log HTML path set to: /Users/<USER>/Desktop/ce-ps3-product-repo/templates/log.html
DEBUG: Ensured output directory exists: /Users/<USER>/Desktop/ce-ps3-product-repo/migration/m2vm
DEBUG: Received Form Data - Move Group: 'mg1', Source Name: ' ali-aws-source-01'
DEBUG: Received File Object - Filename: 'ALI-VM App Mapping.xlsx'
DEBUG: Validating request inputs...
DEBUG: Request input validation successful.
INFO: Calling generate_m2vm_dataframe for Move Group: mg1
INFO: Starting generate_m2vm_dataframe for Move Group: mg1, Source Name:  ali-aws-source-01
DEBUG: Reading uploaded file stream into BytesIO buffer...
DEBUG: Stream content successfully read into BytesIO buffer.
DEBUG: Reading Excel data from BytesIO buffer...
DEBUG: Successfully read Excel data. Shape: (25, 48)
DEBUG: Validating required columns...
DEBUG: Required columns validation successful (essential columns present).
DEBUG: Filtering data for Move Group 'mg1' and Migration Path 'Rehost'...
DEBUG: Filtering complete. Shape after filtering: (2, 48)
DEBUG: Starting data processing and column calculations...
DEBUG: Calculated 'Machine type series'.
DEBUG: Calculated 'Calculated External IP'.
DEBUG: Constructing the final output DataFrame...
DEBUG: Final DataFrame constructed. Shape: (2, 29)
INFO: Exiting generate_m2vm_dataframe successfully.
INFO: generate_m2vm_dataframe returned DataFrame with shape: (2, 29)
DEBUG: Attempting to save result DataFrame to CSV: /Users/<USER>/Desktop/ce-ps3-product-repo/migration/m2vm/mg1.csv
INFO: Successfully saved CSV file: /Users/<USER>/Desktop/ce-ps3-product-repo/migration/m2vm/mg1.csv
INFO: Request processing finished with status code: 0
DEBUG: Attempting to write execution log to HTML file: /Users/<USER>/Desktop/ce-ps3-product-repo/templates/log.html