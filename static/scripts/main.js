function switchCont(invoker) {
    for (let i = 1; i <= 5; i++) {
        const div = document.getElementById('div' + i);
        const button = document.getElementById('buto' + i);
        if (i === invoker) {
            div.style.display = 'block';
            button.classList.add('active');
        } else {
            div.style.display = 'none';
            button.classList.remove('active');
        }
    }
}

function addUUID(form) {
    // This function is not strictly necessary with the new implementation,
    // but we'll keep it for now to avoid breaking any existing logic.
}

function revealText(form) {
    const uuid = crypto.randomUUID().replace(/-/g, '');
    let uuidInput = form.elements["uuid"];
    if (!uuidInput) {
        uuidInput = document.createElement("input");
        uuidInput.type = "hidden";
        uuidInput.name = "uuid";
        form.appendChild(uuidInput);
    }
    uuidInput.value = uuid;

    // --- New Loader Implementation ---
    const overlay = document.createElement("div");
    overlay.id = "loadingOverlay";
    overlay.className = "loading-overlay";

    const loaderContainer = document.createElement("div");
    loaderContainer.className = "loader-container";

    const loaderTitle = document.createElement("h4");
    loaderTitle.className = "loader-title";
    loaderTitle.innerText = "Processing your request...";

    const loaderAnimation = document.createElement("div");
    loaderAnimation.className = "loader-animation";
    for (let i = 0; i < 5; i++) {
        const bar = document.createElement("div");
        bar.className = "bar";
        loaderAnimation.appendChild(bar);
    }

    const statusText = document.createElement("p");
    statusText.className = "loader-status";
    statusText.innerText = "Initializing...";

    const liveOutputContainer = document.createElement("div");
    liveOutputContainer.className = "live-output-container";
    liveOutputContainer.style.display = "none"; // Hidden by default

    const liveOutputPre = document.createElement("pre");
    liveOutputPre.className = "live-output";
    liveOutputContainer.appendChild(liveOutputPre);

    const timerText = document.createElement("p");
    timerText.className = "loader-timer";
    timerText.innerText = "Time: 0s";

    const closeButton = document.createElement("button");
    closeButton.className = "btn-close";
    closeButton.innerHTML = "&times;";
    closeButton.onclick = () => {
        overlay.remove();
        if (eventSource) {
            eventSource.close();
        }
    };

    loaderContainer.appendChild(closeButton);
    loaderContainer.appendChild(loaderTitle);
    loaderContainer.appendChild(loaderAnimation);
    loaderContainer.appendChild(statusText);
    loaderContainer.appendChild(liveOutputContainer);
    loaderContainer.appendChild(timerText);
    overlay.appendChild(loaderContainer);
    document.body.appendChild(overlay);

    // --- Timer Implementation ---
    let totalSeconds = 0;
    const timerInterval = setInterval(() => {
        totalSeconds++;
        let timerString;
        if (totalSeconds < 60) {
            timerString = `${totalSeconds}s`;
        } else {
            const minutes = Math.floor(totalSeconds / 60);
            const seconds = totalSeconds % 60;
            timerString = `${minutes}m ${seconds}s`;
        }
        timerText.innerText = `Time: ${timerString}`;
    }, 1000);

    // --- SSE Implementation ---
    const eventSource = new EventSource('/process_status');

    eventSource.onopen = function() {
        statusText.innerText = "Connection established. Waiting for data...";
    };

    eventSource.onmessage = function(event) {
        try {
            const data = JSON.parse(event.data);

            // Handle different status types
            if (data.status === 'processing') {
                statusText.innerText = `Now processing: ${data.function}...`;
            } else if (data.status === 'log') {
                liveOutputContainer.style.display = "block";
                liveOutputPre.textContent = data.log;
            } else if (data.status === 'heartbeat') {
                // Keep connection alive, no action needed
                console.log('SSE heartbeat received');
            }

            if (data.status === 'completed') {
                eventSource.close();
                clearInterval(timerInterval);
                loaderContainer.innerHTML = `
                    <button class="btn-close" onclick="document.getElementById('loadingOverlay').remove()">&times;</button>
                    <h4 class="loader-title">Processing Complete!</h4>
                    <p class="loader-status">${data.msg || 'Your file is ready for download.'}</p>
                    <div class="completion-actions">
                        ${data.file ? `<a href="/dfile?path=${data.file}" class="btn btn-primary">Download File</a>` : ''}
                    </div>
                `;
                if (data.err) {
                    const errorDetails = document.createElement('p');
                    errorDetails.className = 'text-danger';
                    errorDetails.innerText = `Error: ${data.err}`;
                    loaderContainer.querySelector('.completion-actions').before(errorDetails);
                }
                // Reset the form that was submitted
                form.reset();
            }
        } catch (error) {
            console.error('Error parsing SSE data:', error);
            statusText.innerText = "Error processing server response.";
        }
    };

    eventSource.onerror = function(event) {
        console.error('SSE connection error:', event);
        statusText.innerText = "Connection error. Please try again.";
        eventSource.close();
        clearInterval(timerInterval);
        setTimeout(() => overlay.remove(), 3000);
    };
}

function openCity(evt, cityName) {
    var i, tabcontent, tablinks;
    if (document.getElementById(cityName).style.display === "block") {
        document.getElementById(cityName).style.display = "none";
        evt.currentTarget.classList.remove("active");
    } else {
        tabcontent = document.getElementsByClassName("tabcontent");
        for (i = 0; i < tabcontent.length; i++) {
            tabcontent[i].style.display = "none";
        }
        tablinks = document.getElementsByClassName("tablinks");
        for (i = 0; i < tablinks.length; i++) {
            tablinks[i].className = tablinks[i].className.replace(" active", "");
        }
        document.getElementById(cityName).style.display = "block";
        evt.currentTarget.className += " active";
    }
}

function initCity() {
    var i, tabcontent;
    tabcontent = document.getElementsByClassName("tabcontent");
    for (i = 0; i < tabcontent.length; i++) {
        tabcontent[i].style.display = "none";
    }
}

initCity();

function redirectToLink() {
    window.open("https://docs.google.com/spreadsheets/d/1kq2JisRRh0ePMiEloL1B5oy0dw3fFpvQYGdgwgg4Gjk/edit?usp=drive_link", "_blank");
}

function redirectToLink1() {
    window.open("https://lookerstudio.google.com/reporting/9da7effe-7926-4b42-a441-7b9d3d149395/page/L2qSD", "_blank");
}

function checkFields() {
    function checkButtonState(fieldIds, submitButtonId) {
        const isAnyFieldFilled = fieldIds.some(id => document.getElementById(id).value);
        document.getElementById(submitButtonId).disabled = !isAnyFieldFilled;
    }

    checkButtonState(["orgid", "projids", "folderids"], "gcpin");
    checkButtonState(["orgid1", "projids1", "folderids1"], "gcpin1");
}

["orgid", "projids", "folderids", "orgid1", "projids1", "folderids1"].forEach(id => {
    if (document.getElementById(id)) {
        document.getElementById(id).addEventListener("input", checkFields);
    }
});

checkFields();

document.addEventListener('DOMContentLoaded', function() {
    const sliders = document.querySelectorAll('.switch input[type="checkbox"]');
    sliders.forEach(slider => {
        slider.addEventListener('change', function() {
            const targetId = this.getAttribute('data-target');
            const fieldsContainer = document.getElementById(targetId);
            if (fieldsContainer) {
                if (this.checked) {
                    fieldsContainer.classList.add('show');
                } else {
                    fieldsContainer.classList.remove('show');
                }
            }
        });
    });
});
