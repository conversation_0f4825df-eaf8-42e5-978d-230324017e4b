Region,Source name,Source VM name,Source VM ID,Migration group,Instance name,Project,Zone,Machine type series,Machine type,Disk type,Network,Subnetwork,External IP,Internal IP,Hostname,Network tags,Service account,On host maintenance,Automatic restart,License type,Labels,Node affinity labels,Minimum vCPUs allocated,Metadata,Boot option,Secure boot,vTPM,Replication idle time between cycles
asia-southeast1, ali-aws-source-01,1000ALPHDBA201,i-0afd1588a3ccfb12f,mg1,vm-rbgc-avida-uat-dba-01,projects/prj-ali-cmn-m2vm-sa-01/locations/global/targetProjects/prj-ali-rbgc-avida-uat-svc-01,asia-southeast1-a,n2d,n2d-standard-4,SSD,vpc-cmn-uat-01,projects/prj-ali-cmn-uat-host-01/regions/asia-southeast1/subnetworks/snet-rbgc-avida-uat-vm,,************,,"avida-app-db
iap-ssh",<EMAIL>,MIGRATE,On,,"app:avida
env:uat
applayer:app-db
movegroup:mg1
bu:rbgc",,,,BIOS,,,7200s
asia-southeast1, ali-aws-source-01,1000ALPHDBA301,i-0998149f2de7dc69a,mg1,vm-rbgc-avida-qa-dba-01,projects/prj-ali-cmn-m2vm-sa-01/locations/global/targetProjects/prj-ali-rbgc-avida-qa-svc-01,asia-southeast1-a,n2d,n2d-standard-4,SSD,vpc-cmn-qa-01,projects/prj-ali-cmn-qa-host-01/regions/asia-southeast1/subnetworks/snet-rbgc-avida-qa-vm,,************,,"avida-app-db
iap-ssh",<EMAIL>,MIGRATE,On,,"app:avida
env:qa
applayer:app-db
movegroup:mg1
bu:rbgc",,,,BIOS,,,7200s
