import json
import queue
from flask import Flask, Response
from flask_cors import CORS
from pubsub import pub

from .config import Config
from .utils import sse_queue, setup_logging, sse_listener, get_path

def create_app():
    """Creates and configures the Flask application."""
    app = Flask(
        __name__,
        template_folder=get_path('templates'),
        static_folder=get_path('static')
    )
    app.config.from_object(Config)

    # Initialize extensions and logging
    CORS(app)
    setup_logging()
    
    # Subscribe to pubsub topic for SSE
    pub.subscribe(sse_listener, 'process_status')

    # Import and register blueprints
    from .routes.main import main_bp
    from .routes.api import api_bp
    app.register_blueprint(main_bp)
    app.register_blueprint(api_bp)

    @app.route('/process_status')
    def sse_stream():
        """Server-Sent Events endpoint to stream status to the client."""
        def event_stream():
            while True:
                try:
                    # Use timeout to prevent indefinite blocking
                    data = sse_queue.get(timeout=30)
                    yield f"data: {json.dumps(data)}\n\n"

                    # If the status is 'completed', break the loop
                    if data.get('status') == 'completed':
                        break
                except queue.Empty:
                    # Send a heartbeat to keep connection alive
                    yield f"data: {json.dumps({'status': 'heartbeat'})}\n\n"
                except Exception as e:
                    # Log any other errors and break the connection
                    print(f"SSE error: {e}")
                    break

        response = Response(event_stream(), mimetype='text/event-stream')
        response.headers['Cache-Control'] = 'no-cache'
        response.headers['Connection'] = 'keep-alive'
        response.headers['Access-Control-Allow-Origin'] = '*'
        return response

    return app
